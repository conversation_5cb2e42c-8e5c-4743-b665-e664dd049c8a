<script setup lang="ts">
import { onMounted } from "vue";
import {
  useTimelineContent,
  useCardInteraction,
  useDaySelection,
  useItineraryPlanInit,
  itineraryItems,
  dayItems,
} from "./ItineraryPlanView";
import "./ItineraryPlanView.scss";

// 组件props和emit定义
const props = defineProps<{
  // 目的地城市名称
  cityName?: string;
  // 选择的日期范围（出发日期和返回日期）
  selectedDates?: string[];
  // 去程火车信息
  departureTrainInfo?: {
    trainNumber: string;
    departureTime: string;
    arrivalTime: string;
  };
  // 返程火车信息
  returnTrainInfo?: {
    trainNumber: string;
    departureTime: string;
    arrivalTime: string;
  };
}>();

// 使用组合式函数
const timelineContentUtils = useTimelineContent();
const cardInteractionUtils = useCardInteraction();
const daySelectionUtils = useDaySelection();

const { timelineContentRef, updateItinerary, isLoading, loadingText } =
  timelineContentUtils;
const { toggleCardExpand, handleIconClick, toggleTransportation } =
  cardInteractionUtils;
const { daySelectorRef, handleDaySelect, isDaySelected } = daySelectionUtils;

// 初始化组件
const { initializeComponent } = useItineraryPlanInit(
  timelineContentUtils,
  cardInteractionUtils,
  daySelectionUtils
);

// 处理日期选择
const onDaySelect = (dayNumber: number) => {
  handleDaySelect(dayNumber, updateItinerary);
};

// 处理卡片双击
const onCardDoubleClick = (event: Event) => {
  const card = event.currentTarget as HTMLElement;
  toggleCardExpand(card);
};

// 处理展开图标点击
const onExpandIconClick = (event: Event, card: HTMLElement) => {
  handleIconClick(event, card);
};

// 处理交通方式点击
const onTransportationClick = (event: Event) => {
  const transportation = event.currentTarget as HTMLElement;
  toggleTransportation(transportation);
};

// 在组件挂载后初始化
onMounted(() => {
  // 注意：我们不在这里调用 initializeComponent，而是依赖父组件通过 initItineraryPlanComponent 来初始化
  console.log("ItineraryPlanView 组件已挂载，等待父组件初始化");

  // 如果有火车票信息，打印出来
  if (props.departureTrainInfo) {
    console.log(
      `去程火车信息: ${props.departureTrainInfo.trainNumber}, 出发时间: ${props.departureTrainInfo.departureTime}, 到达时间: ${props.departureTrainInfo.arrivalTime}`
    );
  }
  if (props.returnTrainInfo) {
    console.log(
      `返程火车信息: ${props.returnTrainInfo.trainNumber}, 出发时间: ${props.returnTrainInfo.departureTime}, 到达时间: ${props.returnTrainInfo.arrivalTime}`
    );
  }
});

// 暴露方法给父组件
defineExpose({
  initializeComponent,
});
</script>

<template>
  <div class="content-page" id="step3-content">
    <div class="itinerary-container">
      <!-- 加载状态指示器 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <div class="spinner-circle"></div>
          <div class="spinner-circle-inner"></div>
        </div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>

      <!-- 时间线容器 -->
      <div class="timeline-container" :class="{ loading: isLoading }">
        <!-- 时间线 -->
        <div class="timeline"></div>

        <!-- 时间线内容 -->
        <div class="timeline-content" ref="timelineContentRef">
          <!-- 行程项目 -->
          <template v-for="item in itineraryItems" :key="item.id">
            <!-- 景点项目 -->
            <div v-if="item.type === 'attraction'" class="timeline-item">
              <div class="timeline-dot"></div>
              <div
                :class="['timeline-card', item.position]"
                @dblclick="onCardDoubleClick"
              >
                <div class="timeline-card-title">
                  <span>{{ item.name }}</span>
                  <span
                    class="expand-icon"
                    @click="(e) => onExpandIconClick(e, (e.target as HTMLElement).closest('.timeline-card') as HTMLElement)"
                    >▼</span
                  >
                </div>
                <div class="timeline-card-content">
                  {{ item.description }}
                </div>
                <div class="timeline-card-details">
                  <div class="timeline-card-meta">
                    <span>门票: {{ item.ticketPrice }}</span>
                    <span>推荐停留: {{ item.recommendedStay }}</span>
                  </div>
                  <div class="timeline-card-image">
                    <img :src="item.imageUrl" :alt="item.name" />
                  </div>
                </div>
              </div>
              <div class="timeline-time">{{ item.time }}</div>
            </div>

            <!-- 交通方式项目 -->
            <div
              v-else-if="item.type === 'transportation'"
              class="transportation-item"
            >
              <div class="transportation-dot"></div>
              <div class="transportation" @click="onTransportationClick">
                <div class="transportation-header">
                  <span class="transportation-icon">{{ item.icon }}</span>
                  <span class="transportation-time">{{ item.duration }}</span>
                </div>
                <span class="transportation-detail">{{ item.detail }}</span>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 右侧日期选择器 -->
      <div class="day-selector">
        <div class="day-selector-header">
          <div class="day-title">行程日期</div>
        </div>
        <div class="day-selector-content" ref="daySelectorRef">
          <div
            v-for="day in dayItems"
            :key="day.date"
            :class="['day-item', { selected: isDaySelected(day.number) }]"
            :data-date="day.date"
            @click="() => onDaySelect(day.number)"
          >
            <div class="day-number">{{ day.number }}</div>
            <div class="day-label">{{ day.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
